using System;
using System.Windows.Controls;
using NetworkManagement.ViewModels;

namespace NetworkManagement.Views
{
    public partial class DevicesView : UserControl
    {
        public DevicesView()
        {
            InitializeComponent();
            Loaded += DevicesView_Loaded;
        }

        private async void DevicesView_Loaded(object sender, System.Windows.RoutedEventArgs e)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("DevicesView_Loaded: بدء تحميل صفحة الأجهزة");

                if (DataContext is DevicesViewModel vm)
                {
                    System.Diagnostics.Debug.WriteLine("DevicesView_Loaded: استدعاء InitializeAsync");
                    await vm.InitializeAsync();
                    System.Diagnostics.Debug.WriteLine("DevicesView_Loaded: تم تحميل الصفحة بنجاح");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("DevicesView_Loaded: لا يوجد DevicesViewModel في DataContext");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"DevicesView_Loaded: خطأ - {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"DevicesView_Loaded: تفاصيل الخطأ - {ex}");

                System.Windows.MessageBox.Show(
                    $"خطأ في تحميل صفحة الأجهزة:\n{ex.Message}\n\nتفاصيل إضافية:\n{ex.InnerException?.Message}",
                    "خطأ",
                    System.Windows.MessageBoxButton.OK,
                    System.Windows.MessageBoxImage.Error);
            }
        }
    }
}
