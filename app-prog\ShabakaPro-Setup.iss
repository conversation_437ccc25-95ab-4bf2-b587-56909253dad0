; Inno Setup Script for Shabaka Pro
; نظام إدارة الشبكات والأجهزة المتقدم

#define MyAppName "Shabaka Pro"
#define MyAppNameArabic "شبكة برو - نظام إدارة الشبكات"
#define MyAppVersion "1.0.0"
#define MyAppPublisher "Shabaka Pro"
#define MyAppURL "https://shabaka-pro.com"
#define MyAppExeName "NetworkManagement.exe"
#define MyAppId "{{8B5F3F1A-2C4D-4E5F-9A8B-1C2D3E4F5A6B}"

[Setup]
; NOTE: The value of AppId uniquely identifies this application.
AppId={#MyAppId}
AppName={#MyAppName}
AppVersion={#MyAppVersion}
AppVerName={#MyAppName} {#MyAppVersion}
AppPublisher={#MyAppPublisher}
AppPublisherURL={#MyAppURL}
AppSupportURL={#MyAppURL}
AppUpdatesURL={#MyAppURL}
DefaultDirName={autopf}\{#MyAppName}
DefaultGroupName={#MyAppName}
AllowNoIcons=yes
LicenseFile=
InfoBeforeFile=
InfoAfterFile=
OutputDir=Output
OutputBaseFilename=ShabakaPro-Setup-v{#MyAppVersion}
SetupIconFile=Resources\shabaka-pro.ico
Compression=lzma2/ultra64
SolidCompression=yes
WizardStyle=modern
DisableProgramGroupPage=yes
PrivilegesRequired=admin
ArchitecturesAllowed=x64
ArchitecturesInstallIn64BitMode=x64

; Windows version requirements
MinVersion=6.1sp1
OnlyBelowVersion=0,99

; .NET 6.0 Desktop Runtime requirement will be handled in code section

[Languages]
Name: "arabic"; MessagesFile: "compiler:Languages\Arabic.isl"
Name: "english"; MessagesFile: "compiler:Default.isl"

[CustomMessages]
arabic.LaunchProgram=تشغيل %1
english.LaunchProgram=Launch %1
arabic.CreateDesktopIcon=إنشاء اختصار على سطح المكتب
english.CreateDesktopIcon=Create a &desktop icon
arabic.CreateQuickLaunchIcon=إنشاء اختصار في شريط المهام
english.CreateQuickLaunchIcon=Create a &Quick Launch icon

[Tasks]
Name: "desktopicon"; Description: "{cm:CreateDesktopIcon}"; GroupDescription: "{cm:AdditionalIcons}"; Flags: unchecked
Name: "quicklaunchicon"; Description: "{cm:CreateQuickLaunchIcon}"; GroupDescription: "{cm:AdditionalIcons}"; Flags: unchecked; OnlyBelowVersion: 0,6.1

[Files]
; Main application files
Source: "dist\*"; DestDir: "{app}"; Flags: ignoreversion recursesubdirs createallsubdirs
; Configuration file
Source: "appsettings.json"; DestDir: "{app}"; Flags: ignoreversion

; .NET 6.0 Desktop Runtime (will be downloaded if needed)
; Source: "https://download.microsoft.com/download/3/3/c/33c8de87-6ea0-4f9c-9e30-40f2b2f8b6e8/windowsdesktop-runtime-6.0.36-win-x64.exe"; DestDir: "{tmp}"; Flags: external

[Icons]
Name: "{group}\{#MyAppName}"; Filename: "{app}\{#MyAppExeName}"; IconFilename: "{app}\Resources\shabaka-pro.ico"
Name: "{group}\{cm:UninstallProgram,{#MyAppName}}"; Filename: "{uninstallexe}"
Name: "{autodesktop}\{#MyAppName}"; Filename: "{app}\{#MyAppExeName}"; IconFilename: "{app}\Resources\shabaka-pro.ico"; Tasks: desktopicon
Name: "{userappdata}\Microsoft\Internet Explorer\Quick Launch\{#MyAppName}"; Filename: "{app}\{#MyAppExeName}"; Tasks: quicklaunchicon

[Run]
Filename: "{app}\{#MyAppExeName}"; Description: "{cm:LaunchProgram,{#MyAppName}}"; Flags: nowait postinstall skipifsilent

[UninstallDelete]
Type: filesandordirs; Name: "{app}"

[Registry]
; Create application registry entries
Root: HKCU; Subkey: "Software\{#MyAppName}"; Flags: uninsdeletekeyifempty
Root: HKCU; Subkey: "Software\{#MyAppName}"; ValueType: string; ValueName: "InstallPath"; ValueData: "{app}"
Root: HKCU; Subkey: "Software\{#MyAppName}"; ValueType: string; ValueName: "Version"; ValueData: "{#MyAppVersion}"

[Code]
function GetUninstallString(): String;
var
  sUnInstPath: String;
  sUnInstallString: String;
begin
  sUnInstPath := ExpandConstant('Software\Microsoft\Windows\CurrentVersion\Uninstall\{#emit SetupSetting("AppId")}_is1');
  sUnInstallString := '';
  if not RegQueryStringValue(HKLM, sUnInstPath, 'UninstallString', sUnInstallString) then
    RegQueryStringValue(HKCU, sUnInstPath, 'UninstallString', sUnInstallString);
  Result := sUnInstallString;
end;

function IsUpgrade(): Boolean;
begin
  Result := (GetUninstallString() <> '');
end;

function UnInstallOldVersion(): Integer;
var
  sUnInstallString: String;
  iResultCode: Integer;
begin
  Result := 0;
  sUnInstallString := GetUninstallString();
  if sUnInstallString <> '' then begin
    sUnInstallString := RemoveQuotes(sUnInstallString);
    if Exec(sUnInstallString, '/SILENT /NORESTART /SUPPRESSMSGBOXES','', SW_HIDE, ewWaitUntilTerminated, iResultCode) then
      Result := 3
    else
      Result := 2;
  end else
    Result := 1;
end;

procedure CurStepChanged(CurStep: TSetupStep);
begin
  if (CurStep=ssInstall) then
  begin
    if (IsUpgrade()) then
    begin
      UnInstallOldVersion();
    end;
  end;
end;

function InitializeSetup(): Boolean;
begin
  Result := True;
end;

function PrepareToInstall(var NeedsRestart: Boolean): String;
begin
  Result := '';
  NeedsRestart := False;

  // Check for .NET 6.0 Desktop Runtime
  if not DirExists(ExpandConstant('{commonpf}\dotnet\shared\Microsoft.WindowsDesktop.App')) then
  begin
    MsgBox('تنبيه: قد يتطلب هذا التطبيق .NET 6.0 Desktop Runtime للعمل بشكل صحيح.' + #13#10 +
           'يمكنك تحميله من موقع Microsoft الرسمي إذا لزم الأمر.' + #13#10#13#10 +
           'Warning: This application may require .NET 6.0 Desktop Runtime to work properly.' + #13#10 +
           'You can download it from Microsoft official website if needed.',
           mbInformation, MB_OK);
  end;
end;
