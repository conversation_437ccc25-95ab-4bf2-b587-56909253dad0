using System;
using System.Collections.Generic;
using System.Linq;
using System.Collections.ObjectModel;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using NetworkManagement.Models;
using NetworkManagement.Services;
using NetworkManagement.Views;
using NetworkManagement.Helpers;

namespace NetworkManagement.ViewModels
{
    public partial class DevicesViewModel : ObservableObject
    {
        private readonly IDeviceService _deviceService;
        private readonly IPingService _pingService;

        private readonly IReportExportService _exportService;
        private readonly IAuthService _authService;

        [ObservableProperty]
        private ObservableCollection<Device> devices = new();

        [ObservableProperty]
        private Device? selectedDevice;

        [ObservableProperty]
        private string searchText = string.Empty;

        [ObservableProperty]
        private bool isLoading = false;

        [ObservableProperty]
        private bool isPinging = false;

        [ObservableProperty]
        private string pingStatus = string.Empty;

        [ObservableProperty]
        private bool autoRefreshEnabled = false;

        [ObservableProperty]
        private string statusFilter = string.Empty;

        [ObservableProperty]
        private string typeFilter = string.Empty;

        [ObservableProperty]
        private int selectedDevicesCount = 0;

        // خصائص الصلاحيات
        public bool CanAddDevices => _authService.CanAddData();
        public bool CanEditDevices => _authService.CanEditData();
        public bool CanDeleteDevices => _authService.CanDeleteData();
        public bool CanManageDevices => _authService.CanManageDevices;

        private System.Threading.Timer? _refreshTimer;
        private System.Threading.CancellationTokenSource? _loadCancellationTokenSource;
        private readonly object _loadLock = new object();
        private readonly System.Threading.SemaphoreSlim _loadSemaphore = new System.Threading.SemaphoreSlim(1, 1);

        public DevicesViewModel(IDeviceService deviceService, IPingService pingService, IReportExportService exportService, IAuthService authService)
        {
            _deviceService = deviceService;
            _pingService = pingService;
            _exportService = exportService;
            _authService = authService;

            // Initialize with empty collection - load data when view is ready
            Devices = new ObservableCollection<Device>();

            // الاشتراك في تغيير المستخدم لتحديث الصلاحيات
            _authService.UserChanged += OnUserChanged;


        }

        // Method to initialize data - called from View's Loaded event
        public async System.Threading.Tasks.Task InitializeAsync()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("DevicesViewModel.InitializeAsync: بدء التهيئة");

                // استعادة حالة الفلاتر المحفوظة
                RestoreFilterState();

                await LoadDevicesAsync();
                System.Diagnostics.Debug.WriteLine("DevicesViewModel.InitializeAsync: تمت التهيئة بنجاح");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"DevicesViewModel.InitializeAsync: خطأ - {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"DevicesViewModel.InitializeAsync: تفاصيل الخطأ - {ex}");

                // Show user-friendly error message
                MessageHelper.HandleException(ex, "تحميل البيانات", true);
            }
        }

        [RelayCommand]
        private async System.Threading.Tasks.Task LoadDevicesAsync()
        {
            await LoadDevicesAsync(System.Threading.CancellationToken.None);
        }

        private async System.Threading.Tasks.Task LoadDevicesAsync(System.Threading.CancellationToken cancellationToken)
        {
            // استخدام Semaphore لمنع التحميل المتزامن
            await _loadSemaphore.WaitAsync(cancellationToken).ConfigureAwait(false);

            try
            {
                IsLoading = true;

                // تطبيق فلترة الصلاحيات باستخدام PermissionHelper
                var filteredDevices = await PermissionHelper.LoadFilteredDataAsync(
                    _authService,
                    _deviceService.GetAllAsync,
                    device => device.NetworkId,
                    cancellationToken);

                // التحقق من الإلغاء قبل تحديث UI
                cancellationToken.ThrowIfCancellationRequested();

                // تحديث مجموعة الأجهزة
                await UpdateDevicesCollectionAsync(filteredDevices);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading devices: {ex.Message}");

                // Show error on UI thread
                await System.Windows.Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    MessageHelper.HandleException(ex, "تحميل الأجهزة");
                });
            }
            finally
            {
                IsLoading = false;
                _loadSemaphore.Release();

                // تحديث حالة الأوامر سيتم تلقائياً عند تغيير SelectedDevice
            }
        }

        [RelayCommand]
        private async System.Threading.Tasks.Task SearchDevicesAsync()
        {
            await ApplyFiltersAsync(); // استخدام الفلترة الموحدة
        }

        [RelayCommand]
        private void AddDevice()
        {
            _ = AddDeviceInternalAsync();
        }

        private async System.Threading.Tasks.Task AddDeviceInternalAsync()
        {
            try
            {
                // التحقق من صلاحية إضافة الأجهزة
                if (!CanAddDevices)
                {
                    PermissionHelper.ShowPermissionDeniedMessage("إضافة", "أجهزة جديدة");
                    return;
                }

                var dialogViewModel = App.GetService<DeviceDialogViewModel>();

                // Initialize data first
                await dialogViewModel.InitializeAsync();

                var dialog = new DeviceDialog(dialogViewModel);
                var result = dialog.ShowDialog();

                if (result == true)
                {
                    // مسح الفلاتر لإظهار الجهاز الجديد
                    SearchText = string.Empty;
                    StatusFilter = string.Empty;
                    TypeFilter = string.Empty;

                    // تحديث القائمة
                    await LoadDevicesAsync();
                }
            }
            catch (System.Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in AddDeviceAsync: {ex}");
                System.Windows.MessageBox.Show($"خطأ في فتح نافذة إضافة الجهاز:\n{ex.Message}", "خطأ");
            }
        }

        [RelayCommand]
        private async System.Threading.Tasks.Task EditDeviceAsync(Device? device = null)
        {
            System.Diagnostics.Debug.WriteLine("EditDeviceAsync called");
            try
            {
                var deviceToEdit = device ?? SelectedDevice;
                System.Diagnostics.Debug.WriteLine($"Device to edit: {deviceToEdit?.Id}");

                if (deviceToEdit != null)
                {
                    // التحقق من صلاحية تعديل الجهاز
                    if (!PermissionHelper.CanEditItem(_authService, deviceToEdit.NetworkId))
                    {
                        PermissionHelper.ShowPermissionDeniedMessage("تعديل", "هذا الجهاز");
                        return;
                    }

                    System.Diagnostics.Debug.WriteLine("Creating dialog...");
                    var dialogViewModel = App.GetService<DeviceDialogViewModel>();
                    await dialogViewModel.InitializeAsync(deviceToEdit);

                    var dialog = new DeviceDialog(dialogViewModel);

                    // التأكد من أن النافذة تظهر في المقدمة
                    dialog.Owner = System.Windows.Application.Current.MainWindow;
                    dialog.WindowStartupLocation = System.Windows.WindowStartupLocation.CenterOwner;

                    var result = dialog.ShowDialog();

                    if (result == true)
                    {
                        await LoadDevicesAsync();
                    }
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("No device selected for editing");
                    System.Windows.MessageBox.Show("يرجى تحديد جهاز للتعديل", "تنبيه");
                }
            }
            catch (System.Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in EditDeviceAsync: {ex}");
                System.Windows.MessageBox.Show($"خطأ في فتح نافذة تعديل الجهاز:\n{ex.Message}", "خطأ");
            }
        }

        [RelayCommand]
        private async System.Threading.Tasks.Task DeleteDeviceAsync(Device? device = null)
        {
            var deviceToDelete = device ?? SelectedDevice;
            if (deviceToDelete == null) return;

            // التحقق من صلاحية حذف الجهاز
            if (!PermissionHelper.CanDeleteItem(_authService, deviceToDelete.NetworkId))
            {
                PermissionHelper.ShowPermissionDeniedMessage("حذف", "هذا الجهاز");
                return;
            }

            try
            {
                // تحضير معلومات الجهاز للعرض
                var deviceInfo = MessageHelper.FormatDeviceInfo(
                    deviceToDelete.Responsible, deviceToDelete.Type,
                    deviceToDelete.Location, deviceToDelete.Ip);

                // نافذة تأكيد الحذف
                if (MessageHelper.ShowDeleteConfirmation("الجهاز", deviceInfo))
                {
                    IsLoading = true;
                    await _deviceService.DeleteAsync(deviceToDelete.Id);
                    await LoadDevicesAsync();

                    // رسالة نجاح
                    var deviceName = deviceToDelete.Responsible ?? deviceToDelete.Location ?? "الجهاز المحدد";
                    MessageHelper.ShowSuccessMessage("حذف", "الجهاز", deviceName);
                }
            }
            catch (Exception ex)
            {
                MessageHelper.HandleException(ex, "حذف الجهاز");
            }
            finally
            {
                IsLoading = false;
            }
        }

        [RelayCommand]
        private async System.Threading.Tasks.Task PingAllDevicesAsync()
        {
            try
            {
                IsPinging = true;
                PingStatus = "جاري فحص جميع الأجهزة...";

                var devicesWithIp = DeviceFilterHelper.GetDevicesWithValidIp(Devices).ToList();

                if (!devicesWithIp.Any())
                {
                    MessageHelper.ShowWarningMessage("لا توجد أجهزة بعناوين IP للفحص");
                    return;
                }

                PingStatus = $"جاري فحص {devicesWithIp.Count} جهاز...";

                var pingResults = await _pingService.PingMultipleDevicesAsync(devicesWithIp);
                var deviceStatuses = new Dictionary<string, string>();

                foreach (var result in pingResults)
                {
                    var status = result.Value ? "متصل" : "غير متصل";
                    deviceStatuses[result.Key] = status;
                }

                var updatedCount = await _deviceService.UpdateMultipleDeviceStatusesAsync(deviceStatuses);

                // تحديث العرض
                await LoadDevicesAsync();

                PingStatus = $"تم فحص {updatedCount} جهاز بنجاح";

                // عرض نتائج الفحص
                var connectedCount = deviceStatuses.Count(s => s.Value == "متصل");
                var disconnectedCount = deviceStatuses.Count(s => s.Value == "غير متصل");
                var resultMessage = $"تم فحص {updatedCount} جهاز بنجاح\n" +
                                  $"الأجهزة المتصلة: {connectedCount}\n" +
                                  $"الأجهزة غير المتصلة: {disconnectedCount}";
                MessageHelper.ShowInfoMessage(resultMessage, "نتائج الفحص");
            }
            catch (Exception ex)
            {
                MessageHelper.HandleException(ex, "فحص الأجهزة");
            }
            finally
            {
                IsPinging = false;
                PingStatus = string.Empty;
            }
        }



        [RelayCommand]
        private async System.Threading.Tasks.Task PingSelectedDeviceAsync()
        {
            if (SelectedDevice == null || string.IsNullOrWhiteSpace(SelectedDevice.Ip))
            {
                MessageHelper.ShowWarningMessage("يرجى تحديد جهاز بعنوان IP صالح");
                return;
            }

            try
            {
                IsPinging = true;
                PingStatus = $"جاري فحص {SelectedDevice.Responsible ?? SelectedDevice.Location}...";

                var isOnline = await _pingService.PingDeviceAsync(SelectedDevice.Ip);
                var status = isOnline ? "متصل" : "غير متصل";

                await _deviceService.UpdateDeviceStatusAsync(SelectedDevice.Id, status);

                // تحديث العرض
                await LoadDevicesAsync();

                var statusMessage = isOnline ? "الجهاز متصل ويعمل بشكل طبيعي" : "الجهاز غير متصل أو لا يستجيب";
                var resultMessage = $"نتيجة فحص الجهاز:\n{statusMessage}\n\nعنوان IP: {SelectedDevice.Ip}";

                if (isOnline)
                {
                    MessageHelper.ShowInfoMessage(resultMessage, "نتيجة الفحص");
                }
                else
                {
                    MessageHelper.ShowWarningMessage(resultMessage, "نتيجة الفحص");
                }
            }
            catch (Exception ex)
            {
                MessageHelper.HandleException(ex, "فحص الجهاز");
            }
            finally
            {
                IsPinging = false;
                PingStatus = string.Empty;
            }
        }





        [RelayCommand]
        private async System.Threading.Tasks.Task ExportDevicesAsync()
        {
            try
            {
                IsLoading = true;

                var filter = "Excel Files (*.xlsx)|*.xlsx|CSV Files (*.csv)|*.csv";
                var defaultFileName = $"الأجهزة_{DateTime.Now:yyyy-MM-dd}";

                var filePath = await _exportService.GetSaveFilePathAsync(defaultFileName, filter);
                if (string.IsNullOrEmpty(filePath))
                    return;

                string result = "";
                var isExcel = filePath.EndsWith(".xlsx", StringComparison.OrdinalIgnoreCase);

                // تطبيق فلترة الصلاحيات على التصدير
                var filteredDevices = await PermissionHelper.LoadFilteredDataAsync(
                    _authService,
                    _deviceService.GetAllAsync,
                    device => device.NetworkId);

                result = isExcel
                    ? await _exportService.ExportDevicesReportToExcelAsync(filteredDevices, filePath)
                    : await _exportService.ExportDevicesReportToCsvAsync(filteredDevices, filePath);

                if (!string.IsNullOrEmpty(result))
                {
                    MessageHelper.ShowSuccessMessage("تصدير", "بيانات الأجهزة", $"تم الحفظ في:\n{result}");
                }
            }
            catch (Exception ex)
            {
                MessageHelper.HandleException(ex, "تصدير البيانات");
            }
            finally
            {
                IsLoading = false;
            }
        }

        [RelayCommand]
        private async System.Threading.Tasks.Task ImportDevicesAsync()
        {
            // التحقق من صلاحية إضافة الأجهزة
            if (!CanAddDevices)
            {
                PermissionHelper.ShowPermissionDeniedMessage("استيراد", "أجهزة جديدة");
                return;
            }

            try
            {
                IsLoading = true;

                var filter = "Excel Files (*.xlsx)|*.xlsx|CSV Files (*.csv)|*.csv";
                var filePath = await _exportService.GetOpenFilePathAsync(filter);
                if (string.IsNullOrEmpty(filePath))
                    return;

                var confirmMessage = "هل تريد استيراد الأجهزة من الملف المحدد؟\n\n" +
                                   "⚠️ تحذير:\n" +
                                   "• سيتم إضافة الأجهزة الجديدة إلى القائمة الحالية\n" +
                                   "• تأكد من صحة تنسيق الملف\n" +
                                   "• يجب أن تحتوي الأعمدة على: الموقع، النوع، عنوان IP، المسؤول\n\n" +
                                   "هل تريد المتابعة؟";

                if (!MessageHelper.ShowConfirmation(confirmMessage, "تأكيد الاستيراد"))
                    return;

                IEnumerable<Device> importedDevices;
                var isExcel = filePath.EndsWith(".xlsx", StringComparison.OrdinalIgnoreCase);

                if (isExcel)
                {
                    importedDevices = await _exportService.ImportDevicesFromExcelAsync(filePath);
                }
                else
                {
                    importedDevices = await _exportService.ImportDevicesFromCsvAsync(filePath);
                }

                var devicesList = importedDevices.ToList();
                if (!devicesList.Any())
                {
                    var noDataMessage = "لم يتم العثور على بيانات صالحة في الملف.\n\n" +
                                      "تأكد من:\n• وجود بيانات في الملف\n• صحة تنسيق الأعمدة\n• وجود سطر العناوين";
                    MessageHelper.ShowWarningMessage(noDataMessage, "لا توجد بيانات");
                    return;
                }

                // Save imported devices
                int successCount = 0;
                int errorCount = 0;
                var errors = new List<string>();

                foreach (var device in devicesList)
                {
                    try
                    {
                        // Validate required fields
                        if (string.IsNullOrWhiteSpace(device.Responsible) && string.IsNullOrWhiteSpace(device.Location))
                        {
                            errorCount++;
                            errors.Add($"جهاز بدون مسؤول أو موقع محدد");
                            continue;
                        }

                        await _deviceService.CreateAsync(device);
                        successCount++;
                    }
                    catch (Exception ex)
                    {
                        errorCount++;
                        errors.Add($"خطأ في حفظ جهاز {device.Responsible ?? device.Location}: {ex.Message}");
                    }
                }

                // Refresh the list
                await LoadDevicesAsync();

                // عرض نتائج الاستيراد
                MessageHelper.ShowOperationResults("استيراد", successCount, errorCount, errors.ToArray());
            }
            catch (Exception ex)
            {
                MessageHelper.HandleException(ex, "استيراد البيانات");
            }
            finally
            {
                IsLoading = false;
            }
        }

        // معالج تغيير الجهاز المحدد
        partial void OnSelectedDeviceChanged(Device? value)
        {
            // تحديث حالة أوامر التعديل والحذف سيتم تلقائياً
            // عبر نظام CommunityToolkit.Mvvm
        }

        // معالج تغيير نص البحث للبحث التلقائي
        partial void OnSearchTextChanged(string value)
        {
            // تأخير البحث لتجنب البحث المفرط أثناء الكتابة
            _ = DelayedSearchAsync(value);

            // حفظ حالة الفلتر
            SaveFilterState();
        }

        private async System.Threading.Tasks.Task DelayedSearchAsync(string searchValue)
        {
            try
            {
                await System.Threading.Tasks.Task.Delay(500); // انتظار نصف ثانية
                if (SearchText == searchValue) // التأكد أن النص لم يتغير
                {
                    await System.Windows.Application.Current.Dispatcher.InvokeAsync(async () =>
                    {
                        await SearchDevicesAsync();
                    });
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in delayed search: {ex.Message}");
            }
        }

        [RelayCommand]
        private void ToggleAutoRefresh()
        {
            AutoRefreshEnabled = !AutoRefreshEnabled;

            if (AutoRefreshEnabled)
            {
                StartAutoRefresh();
            }
            else
            {
                StopAutoRefresh();
            }
        }

        private void StartAutoRefresh()
        {
            StopAutoRefresh(); // إيقاف أي تحديث سابق

            // تحديث كل 30 ثانية
            _refreshTimer = new System.Threading.Timer(async _ =>
            {
                if (!IsPinging && !IsLoading)
                {
                    await System.Windows.Application.Current.Dispatcher.InvokeAsync(async () =>
                    {
                        await PingAllDevicesAsync();
                    });
                }
            }, null, TimeSpan.FromSeconds(30), TimeSpan.FromSeconds(30));
        }

        private void StopAutoRefresh()
        {
            _refreshTimer?.Dispose();
            _refreshTimer = null;
        }

        [RelayCommand]
        private async System.Threading.Tasks.Task ClearFiltersAsync()
        {
            SearchText = string.Empty;
            StatusFilter = string.Empty;
            TypeFilter = string.Empty;
            await LoadDevicesAsync();
        }

        /// <summary>
        /// حفظ حالة الفلاتر الحالية
        /// </summary>
        public void SaveFilterState()
        {
            try
            {
                var filterState = new
                {
                    SearchText = SearchText ?? string.Empty,
                    StatusFilter = StatusFilter ?? string.Empty,
                    TypeFilter = TypeFilter ?? string.Empty
                };

                var json = System.Text.Json.JsonSerializer.Serialize(filterState);

                // حفظ في Registry باستخدام نفس آلية SettingsService
                using (var key = Microsoft.Win32.Registry.CurrentUser.CreateSubKey(@"SOFTWARE\NetworkManagement"))
                {
                    key?.SetValue("DevicesFilterState", json);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error saving filter state: {ex.Message}");
            }
        }

        /// <summary>
        /// استعادة حالة الفلاتر المحفوظة
        /// </summary>
        public void RestoreFilterState()
        {
            try
            {
                // قراءة من Registry
                using (var key = Microsoft.Win32.Registry.CurrentUser.OpenSubKey(@"SOFTWARE\NetworkManagement"))
                {
                    var json = key?.GetValue("DevicesFilterState")?.ToString();
                    if (!string.IsNullOrEmpty(json))
                    {
                        var filterState = System.Text.Json.JsonSerializer.Deserialize<System.Text.Json.JsonElement>(json);

                        // استعادة الفلاتر بدون تشغيل البحث التلقائي
                        if (filterState.TryGetProperty("SearchText", out var searchTextElement))
                        {
                            SearchText = searchTextElement.GetString() ?? string.Empty;
                        }

                        if (filterState.TryGetProperty("StatusFilter", out var statusFilterElement))
                        {
                            StatusFilter = statusFilterElement.GetString() ?? string.Empty;
                        }

                        if (filterState.TryGetProperty("TypeFilter", out var typeFilterElement))
                        {
                            TypeFilter = typeFilterElement.GetString() ?? string.Empty;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error restoring filter state: {ex.Message}");
            }
        }

        // معالج تغيير فلتر الحالة
        partial void OnStatusFilterChanged(string value)
        {
            _ = DelayedFilterAsync();
            SaveFilterState();
        }

        // معالج تغيير فلتر النوع
        partial void OnTypeFilterChanged(string value)
        {
            _ = DelayedFilterAsync();
            SaveFilterState();
        }

        private async System.Threading.Tasks.Task DelayedFilterAsync()
        {
            try
            {
                await System.Threading.Tasks.Task.Delay(300);
                await System.Windows.Application.Current.Dispatcher.InvokeAsync(async () =>
                {
                    await ApplyFiltersAsync();
                });
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in delayed filter: {ex.Message}");
            }
        }

        private async System.Threading.Tasks.Task ApplyFiltersAsync()
        {
            try
            {
                IsLoading = true;

                // تحميل جميع الأجهزة مع تطبيق فلترة الصلاحيات
                var allDevices = await PermissionHelper.LoadFilteredDataAsync(
                    _authService,
                    _deviceService.GetAllAsync,
                    device => device.NetworkId);

                // تطبيق جميع الفلاتر باستخدام Helper موحد
                var filteredDevices = DeviceFilterHelper.ApplyAllFilters(
                    allDevices,
                    SearchText,
                    StatusFilter,
                    TypeFilter);

                await UpdateDevicesCollectionAsync(filteredDevices);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error applying filters: {ex.Message}");
                MessageHelper.HandleException(ex, "تطبيق الفلاتر");
            }
            finally
            {
                IsLoading = false;
            }
        }

        /// <summary>
        /// تحديث مجموعة الأجهزة في الواجهة
        /// </summary>
        /// <param name="devices">الأجهزة الجديدة</param>
        private async System.Threading.Tasks.Task UpdateDevicesCollectionAsync(IEnumerable<Device> devices)
        {
            await System.Windows.Application.Current.Dispatcher.InvokeAsync(() =>
            {
                // إلغاء الاشتراك في الأجهزة القديمة لمنع تسريب الذاكرة
                foreach (var oldDevice in Devices)
                {
                    oldDevice.PropertyChanged -= Device_PropertyChanged;
                }

                Devices.Clear();
                foreach (var device in devices)
                {
                    // الاشتراك في PropertyChanged للتحديد
                    device.PropertyChanged += Device_PropertyChanged;
                    Devices.Add(device);
                }
                UpdateSelectedDevicesCount();
            });
        }

        [RelayCommand]
        private static void OpenIpInBrowser(string? ip)
        {
            if (string.IsNullOrWhiteSpace(ip))
                return;

            try
            {
                var url = ip.StartsWith("http") ? ip : $"http://{ip}";
                System.Diagnostics.Process.Start(new System.Diagnostics.ProcessStartInfo
                {
                    FileName = url,
                    UseShellExecute = true
                });
            }
            catch (Exception ex)
            {
                MessageHelper.ShowErrorMessage($"فتح العنوان {ip} في المتصفح", ex.Message);
            }
        }

        [RelayCommand]
        private void SelectAllDevices()
        {
            var allSelected = Devices.All(d => d.IsSelected);

            // تحديث التحديد على UI Thread
            System.Windows.Application.Current?.Dispatcher?.Invoke(() =>
            {
                foreach (var device in Devices)
                {
                    device.IsSelected = !allSelected;
                }
                UpdateSelectedDevicesCount();
            });
        }

        private void UpdateSelectedDevicesCount()
        {
            SelectedDevicesCount = DeviceFilterHelper.GetSelectedDevices(Devices).Count();
        }

        private void Device_PropertyChanged(object? sender, System.ComponentModel.PropertyChangedEventArgs e)
        {
            if (e.PropertyName == nameof(Device.IsSelected))
            {
                // تحديث العداد على UI Thread
                System.Windows.Application.Current?.Dispatcher?.Invoke(() =>
                {
                    UpdateSelectedDevicesCount();
                });
            }
        }

        [RelayCommand]
        private async System.Threading.Tasks.Task PingSelectedDevicesAsync()
        {
            var selectedDevices = DeviceFilterHelper.GetSelectedDevicesWithValidIp(Devices).ToList();

            if (!selectedDevices.Any())
            {
                MessageHelper.ShowWarningMessage("يرجى تحديد أجهزة بعناوين IP صالحة للفحص");
                return;
            }

            try
            {
                IsPinging = true;
                PingStatus = $"جاري فحص {selectedDevices.Count} جهاز محدد...";

                var pingResults = await _pingService.PingMultipleDevicesAsync(selectedDevices);
                var deviceStatuses = new Dictionary<string, string>();

                foreach (var result in pingResults)
                {
                    var status = result.Value ? "متصل" : "غير متصل";
                    // المفتاح هو Device ID مباشرة من PingService
                    deviceStatuses[result.Key] = status;
                }

                var updatedCount = await _deviceService.UpdateMultipleDeviceStatusesAsync(deviceStatuses);

                // حفظ التحديد الحالي
                var selectedIds = selectedDevices.Select(d => d.Id).ToHashSet();

                // تحديث العرض
                await LoadDevicesAsync();

                // استعادة التحديد على UI Thread
                System.Windows.Application.Current?.Dispatcher?.Invoke(() =>
                {
                    foreach (var device in Devices.Where(d => selectedIds.Contains(d.Id)))
                    {
                        device.IsSelected = true;
                    }
                });

                PingStatus = $"تم فحص {updatedCount} جهاز محدد بنجاح";

                // عرض نتائج فحص الأجهزة المحددة
                var connectedCount = deviceStatuses.Count(s => s.Value == "متصل");
                var disconnectedCount = deviceStatuses.Count(s => s.Value == "غير متصل");
                var resultMessage = $"تم فحص {updatedCount} جهاز محدد بنجاح\n" +
                                  $"الأجهزة المتصلة: {connectedCount}\n" +
                                  $"الأجهزة غير المتصلة: {disconnectedCount}";
                MessageHelper.ShowInfoMessage(resultMessage, "نتائج الفحص");
            }
            catch (Exception ex)
            {
                MessageHelper.HandleException(ex, "فحص الأجهزة المحددة");
            }
            finally
            {
                IsPinging = false;
                PingStatus = string.Empty;
            }
        }

        [RelayCommand]
        private async System.Threading.Tasks.Task DeleteSelectedDevicesAsync()
        {
            var selectedDevices = DeviceFilterHelper.GetSelectedDevices(Devices).ToList();

            if (!selectedDevices.Any())
            {
                MessageHelper.ShowWarningMessage("يرجى تحديد أجهزة للحذف");
                return;
            }

            // تحضير أسماء الأجهزة للعرض
            var deviceNames = selectedDevices.Select(d => d.Responsible ?? d.Location ?? "جهاز غير محدد").ToArray();

            if (MessageHelper.ShowMultipleDeleteConfirmation("جهاز", selectedDevices.Count, deviceNames))
            {
                try
                {
                    IsLoading = true;
                    int deletedCount = 0;
                    var errors = new List<string>();

                    foreach (var device in selectedDevices)
                    {
                        try
                        {
                            await _deviceService.DeleteAsync(device.Id);
                            deletedCount++;
                        }
                        catch (Exception ex)
                        {
                            errors.Add($"فشل حذف {device.Responsible ?? device.Location}: {ex.Message}");
                        }
                    }

                    await LoadDevicesAsync();

                    // عرض نتائج الحذف
                    MessageHelper.ShowOperationResults("حذف", deletedCount, errors.Count, errors.ToArray());
                }
                catch (Exception ex)
                {
                    MessageHelper.HandleException(ex, "حذف الأجهزة");
                }
                finally
                {
                    IsLoading = false;
                }
            }
        }

        [RelayCommand]
        private async System.Threading.Tasks.Task ExportSelectedDevicesAsync()
        {
            var selectedDevices = DeviceFilterHelper.GetSelectedDevices(Devices).ToList();

            if (!selectedDevices.Any())
            {
                MessageHelper.ShowWarningMessage("يرجى تحديد أجهزة للتصدير");
                return;
            }

            try
            {
                IsLoading = true;

                var filter = "Excel Files (*.xlsx)|*.xlsx|CSV Files (*.csv)|*.csv";
                var defaultFileName = $"الأجهزة_المحددة_{DateTime.Now:yyyy-MM-dd}";

                var filePath = await _exportService.GetSaveFilePathAsync(defaultFileName, filter);
                if (string.IsNullOrEmpty(filePath))
                    return;

                string result = "";
                var isExcel = filePath.EndsWith(".xlsx", StringComparison.OrdinalIgnoreCase);

                result = isExcel
                    ? await _exportService.ExportDevicesReportToExcelAsync(selectedDevices, filePath)
                    : await _exportService.ExportDevicesReportToCsvAsync(selectedDevices, filePath);

                if (!string.IsNullOrEmpty(result))
                {
                    MessageHelper.ShowSuccessMessage("تصدير", $"{selectedDevices.Count} جهاز محدد", $"تم الحفظ في:\n{result}");
                }
            }
            catch (Exception ex)
            {
                MessageHelper.HandleException(ex, "تصدير الأجهزة المحددة");
            }
            finally
            {
                IsLoading = false;
            }
        }

        // معالج تغيير المستخدم لتحديث الصلاحيات
        private async void OnUserChanged(object? sender, User? user)
        {
            try
            {
                lock (_loadLock)
                {
                    // إلغاء أي عملية تحميل سابقة
                    _loadCancellationTokenSource?.Cancel();
                    _loadCancellationTokenSource?.Dispose();
                    _loadCancellationTokenSource = new System.Threading.CancellationTokenSource();
                }

                // تحديث خصائص الصلاحيات على UI thread
                await System.Windows.Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    OnPropertyChanged(nameof(CanAddDevices));
                    OnPropertyChanged(nameof(CanEditDevices));
                    OnPropertyChanged(nameof(CanDeleteDevices));
                    OnPropertyChanged(nameof(CanManageDevices));
                });

                // تأخير قصير لتجنب التحديثات المتكررة
                await System.Threading.Tasks.Task.Delay(100, _loadCancellationTokenSource.Token);

                // إعادة تحميل البيانات حسب الصلاحيات الجديدة
                await LoadDevicesAsync(_loadCancellationTokenSource.Token);
            }
            catch (System.OperationCanceledException)
            {
                // تم إلغاء العملية - هذا طبيعي
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in OnUserChanged: {ex.Message}");
            }
        }

        // تنظيف الموارد
        public void Dispose()
        {
            StopAutoRefresh();

            // إلغاء الاشتراك في تغيير المستخدم
            _authService.UserChanged -= OnUserChanged;

            // إلغاء أي عمليات تحميل جارية
            lock (_loadLock)
            {
                _loadCancellationTokenSource?.Cancel();
                _loadCancellationTokenSource?.Dispose();
                _loadCancellationTokenSource = null;
            }

            // تنظيف Semaphore
            _loadSemaphore?.Dispose();

            // Unsubscribe from all device events to prevent memory leaks
            foreach (var device in Devices)
            {
                device.PropertyChanged -= Device_PropertyChanged;
            }

            Devices.Clear();
        }
    }
}

