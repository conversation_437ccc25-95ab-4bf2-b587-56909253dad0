using System;
using System.Linq;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using NetworkManagement.Services;
using NetworkManagement.Views;
using NetworkManagement.Helpers;
using NetworkManagement.Models;

namespace NetworkManagement.ViewModels
{
    public partial class DashboardViewModel : ObservableObject
    {
        private readonly IDeviceService _deviceService;
        private readonly ISiteService _siteService;
        private readonly ITaskService _taskService;
        private readonly IPurchaseService _purchaseService;
        private readonly IInventoryService _inventoryService;
        private readonly IAuthService _authService;

        [ObservableProperty]
        private int totalDevices;

        [ObservableProperty]
        private int activeDevices;

        [ObservableProperty]
        private int totalSites;

        [ObservableProperty]
        private int pendingTasks;

        [ObservableProperty]
        private decimal monthlySpending;

        [ObservableProperty]
        private int lowStockItems;

        [ObservableProperty]
        private bool isLoading = true;

        // خصائص الصلاحيات
        public bool CanAddDevices => _authService.CanAddData();
        public bool CanAddSites => _authService.CanAddData();
        public bool CanViewReports => _authService.CanViewReports;
        public bool CanManageDevices => _authService.CanManageDevices;
        public bool CanManageSites => _authService.CanManageSites;

        public DashboardViewModel(
            IDeviceService deviceService,
            ISiteService siteService,
            ITaskService taskService,
            IPurchaseService purchaseService,
            IInventoryService inventoryService,
            IAuthService authService)
        {
            _deviceService = deviceService;
            _siteService = siteService;
            _taskService = taskService;
            _purchaseService = purchaseService;
            _inventoryService = inventoryService;
            _authService = authService;

            // الاشتراك في تغييرات المستخدم لتحديث الصلاحيات والبيانات
            _authService.UserChanged += OnUserChanged;
        }

        // Method to initialize data - called from View's Loaded event
        public async System.Threading.Tasks.Task InitializeAsync()
        {
            try
            {
                await LoadDashboardDataAsync();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error initializing dashboard: {ex.Message}");
                // Show user-friendly error message
                System.Windows.MessageBox.Show(
                    $"حدث خطأ أثناء تحميل لوحة التحكم:\n{ex.Message}",
                    "خطأ في التحميل",
                    System.Windows.MessageBoxButton.OK,
                    System.Windows.MessageBoxImage.Error);
            }
        }

        private async System.Threading.Tasks.Task LoadDashboardDataAsync()
        {
            try
            {
                IsLoading = true;

                // استخدام PermissionHelper للحصول على فلتر الشبكة
                var networkFilter = PermissionHelper.GetNetworkFilter(_authService);

                // Load devices data
                var devices = await _deviceService.GetAllAsync(networkFilter);
                TotalDevices = devices.Count();
                // تحقق من حالات الأجهزة المختلفة (active, نشط)
                ActiveDevices = devices.Count(d =>
                    d.Status == "active" ||
                    d.Status == "نشط" ||
                    d.Status?.ToLower() == "active");

                // Load sites data
                var sites = await _siteService.GetAllAsync(networkFilter);
                TotalSites = sites.Count();

                // Load tasks data
                var tasks = await _taskService.GetAllAsync();
                // فلترة المهام حسب الصلاحيات باستخدام PermissionHelper
                var filteredTasks = PermissionHelper.ApplyPermissionFilter(tasks, _authService, task => task.NetworkId);
                PendingTasks = filteredTasks.Count(t => t.Status == "pending");

                // Load monthly spending
                var startOfMonth = new DateTime(DateTime.Now.Year, DateTime.Now.Month, 1);
                var endOfMonth = startOfMonth.AddMonths(1).AddDays(-1);
                MonthlySpending = await _purchaseService.GetTotalSpentAsync(networkFilter, startOfMonth, endOfMonth);

                // Load low stock items
                var lowStock = await _inventoryService.GetLowStockItemsAsync(networkFilter);
                LowStockItems = lowStock.Count();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading dashboard data: {ex.Message}");
            }
            finally
            {
                IsLoading = false;
            }
        }

        [RelayCommand]
        private async System.Threading.Tasks.Task RefreshDataAsync()
        {
            await LoadDashboardDataAsync();
        }

        [RelayCommand]
        private void AddNewDevice()
        {
            _ = AddNewDeviceInternalAsync();
        }

        private async System.Threading.Tasks.Task AddNewDeviceInternalAsync()
        {
            try
            {
                // التحقق من صلاحية إضافة الأجهزة
                if (!CanAddDevices)
                {
                    PermissionHelper.ShowPermissionDeniedMessage("إضافة", "أجهزة جديدة");
                    return;
                }

                var dialogViewModel = App.GetService<DeviceDialogViewModel>();
                await dialogViewModel.InitializeAsync(); // Initialize data first

                var dialog = new DeviceDialog(dialogViewModel);
                var result = dialog.ShowDialog();

                if (result == true)
                {
                    await RefreshDataAsync();
                }
            }
            catch (System.Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in AddNewDeviceAsync: {ex}");
                System.Windows.MessageBox.Show($"خطأ في فتح نافذة إضافة الجهاز:\n{ex.Message}", "خطأ");
            }
        }

        [RelayCommand]
        private void AddNewSite()
        {
            // التحقق من صلاحية إضافة المواقع
            if (!CanAddSites)
            {
                PermissionHelper.ShowPermissionDeniedMessage("إضافة", "مواقع جديدة");
                return;
            }

            var dialogViewModel = App.GetService<SiteDialogViewModel>();
            var dialog = new SiteDialog(dialogViewModel);

            dialogViewModel.SiteSaved += async (s, site) =>
            {
                await RefreshDataAsync();
            };

            dialog.ShowDialog();
        }

        [RelayCommand]
        private void ViewReports()
        {
            // التحقق من صلاحية عرض التقارير
            if (!CanViewReports)
            {
                PermissionHelper.ShowPermissionDeniedMessage("عرض", "التقارير");
                return;
            }

            // Navigate to reports page - simple message for now
            System.Windows.MessageBox.Show(
                "سيتم توجيهك إلى صفحة التقارير قريباً",
                "التقارير",
                System.Windows.MessageBoxButton.OK,
                System.Windows.MessageBoxImage.Information);
        }

        // معالج تغيير المستخدم لتحديث الصلاحيات والبيانات
        private async void OnUserChanged(object? sender, User? user)
        {
            try
            {
                // تحديث خصائص الصلاحيات على UI thread
                await System.Windows.Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    OnPropertyChanged(nameof(CanAddDevices));
                    OnPropertyChanged(nameof(CanAddSites));
                    OnPropertyChanged(nameof(CanViewReports));
                    OnPropertyChanged(nameof(CanManageDevices));
                    OnPropertyChanged(nameof(CanManageSites));
                });

                // تأخير قصير لتجنب التحديثات المتكررة
                await System.Threading.Tasks.Task.Delay(100);

                // إعادة تحميل البيانات حسب الصلاحيات الجديدة
                await LoadDashboardDataAsync();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in OnUserChanged: {ex.Message}");
            }
        }
    }
}
