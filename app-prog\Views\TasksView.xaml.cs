using System;
using System.Windows.Controls;
using Microsoft.Extensions.DependencyInjection;
using NetworkManagement.ViewModels;

namespace NetworkManagement.Views
{
    public partial class TasksView : UserControl, IDisposable
    {
        private IServiceScope? _scope;
        private TasksViewModel? _viewModel;

        public TasksView()
        {
            InitializeComponent();
            Loaded += TasksView_Loaded;
            Unloaded += TasksView_Unloaded;
        }

        private async void TasksView_Loaded(object sender, System.Windows.RoutedEventArgs e)
        {
            try
            {
                if (_viewModel == null)
                {
                    _scope = App.CreateScope();
                    _viewModel = _scope.ServiceProvider.GetRequiredService<TasksViewModel>();
                    DataContext = _viewModel;

                    await _viewModel.InitializeAsync();
                }
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show(
                    $"خطأ في تحميل صفحة المهام:\n{ex.Message}",
                    "خطأ",
                    System.Windows.MessageBoxButton.OK,
                    System.Windows.MessageBoxImage.Error);
            }
        }

        private void TasksView_Unloaded(object sender, System.Windows.RoutedEventArgs e)
        {
            Dispose();
        }

        public void Dispose()
        {
            try
            {
                _viewModel?.Dispose();
                _scope?.Dispose();
                _viewModel = null;
                _scope = null;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error disposing TasksView: {ex.Message}");
            }
            GC.SuppressFinalize(this);
        }
    }
}
