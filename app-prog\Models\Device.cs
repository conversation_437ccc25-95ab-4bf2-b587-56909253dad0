using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel;

namespace NetworkManagement.Models
{
    public class Device : INotifyPropertyChanged
    {
        [Key]
        public string Id { get; set; } = Guid.NewGuid().ToString();

        [MaxLength(100)]
        public string? Responsible { get; set; }

        [MaxLength(50)]
        public string? Type { get; set; }

        [MaxLength(200)]
        public string? Location { get; set; }

        [MaxLength(20)]
        public string? Phone { get; set; }

        [MaxLength(15)]
        public string? Ip { get; set; }

        public DateTime? InstallDate { get; set; }

        [MaxLength(100)]
        public string? PowerConnection { get; set; }

        [MaxLength(50)]
        public string? AdapterType { get; set; }

        public int? NetworkCableLength { get; set; }

        public int? PowerCableLength { get; set; }

        [MaxLength(50)]
        public string? ConnectionMethod { get; set; }

        [MaxLength(50)]
        public string? LinkedNetwork { get; set; }

        [MaxLength(100)]
        public string? BroadcastNetworkName { get; set; }

        public int? Channel { get; set; }

        public int? ConnectedDevices { get; set; }

        [MaxLength(20)]
        public string? Status { get; set; } // active, inactive, maintenance

        public DateTime? LastCheck { get; set; }

        [MaxLength(50)]
        public string? SiteId { get; set; }

        // Network relationship
        public string? NetworkId { get; set; }
        public virtual Network? Network { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.Now;

        public DateTime UpdatedAt { get; set; } = DateTime.Now;

        // Navigation properties
        public virtual Site? Site { get; set; }

        // UI properties (not stored in database)
        [NotMapped]
        private bool _isSelected = false;

        public bool IsSelected
        {
            get => _isSelected;
            set
            {
                if (_isSelected != value)
                {
                    _isSelected = value;
                    OnPropertyChanged(nameof(IsSelected));
                }
            }
        }

        protected virtual void OnPropertyChanged(string propertyName)
        {
            if (System.Windows.Application.Current?.Dispatcher?.CheckAccess() == true)
            {
                PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
            }
            else
            {
                System.Windows.Application.Current?.Dispatcher?.Invoke(() =>
                {
                    PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
                });
            }
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        // Display properties
        public string StatusDisplay => Status switch
        {
            "active" => "نشط",
            "inactive" => "غير نشط",
            "maintenance" => "صيانة",
            "disabled" => "معطل",
            "متصل" => "متصل",
            "غير متصل" => "غير متصل",
            "صيانة" => "صيانة",
            _ => Status ?? "غير محدد"
        };

        public string InstallDateDisplay => InstallDate?.ToString("yyyy/MM/dd") ?? "غير محدد";
        public string LastCheckDisplay => LastCheck?.ToString("yyyy/MM/dd HH:mm") ?? "لم يتم الفحص";
        public string NetworkCableLengthDisplay => NetworkCableLength?.ToString() + " متر" ?? "غير محدد";
        public string PowerCableLengthDisplay => PowerCableLength?.ToString() + " متر" ?? "غير محدد";
        public string ChannelDisplay => Channel?.ToString() ?? "غير محدد";
        public string ConnectedDevicesDisplay => ConnectedDevices?.ToString() + " جهاز" ?? "0 جهاز";
    }
}
