using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using NetworkManagement.Data;
using NetworkManagement.Models;

namespace NetworkManagement.Services
{
    public class InventoryService : IInventoryService
    {
        private readonly IServiceProvider _serviceProvider;

        public InventoryService(IServiceProvider serviceProvider)
        {
            _serviceProvider = serviceProvider;
        }

        public async Task<IEnumerable<Inventory>> GetAllAsync(string? networkFilter = null)
        {
            using var scope = _serviceProvider.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<NetworkDbContext>();

            var query = context.Inventory?.Include(i => i.Network).AsQueryable() ??
                throw new InvalidOperationException("DbSet<Inventory> is null");

            if (!string.IsNullOrEmpty(networkFilter))
            {
                query = query.Where(i => i.NetworkId == networkFilter);
            }

            return await query.OrderBy(i => i.Name).ToListAsync().ConfigureAwait(false);
        }

        public async Task<Inventory?> GetByIdAsync(string id)
        {
            using var scope = _serviceProvider.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<NetworkDbContext>();

            if (context.Inventory == null)
                throw new InvalidOperationException("DbSet<Inventory> is null");

            return await context.Inventory.FindAsync(id).ConfigureAwait(false);
        }

        public async Task<Inventory> CreateAsync(Inventory inventory)
        {
            using var scope = _serviceProvider.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<NetworkDbContext>();

            if (context.Inventory == null)
                throw new InvalidOperationException("DbSet<Inventory> is null");

            inventory.Id = Guid.NewGuid().ToString();
            inventory.CreatedAt = DateTime.Now;
            inventory.LastUpdated = DateTime.Now;
            context.Inventory.Add(inventory);
            await context.SaveChangesAsync().ConfigureAwait(false);
            return inventory;
        }

        public async Task<Inventory> UpdateAsync(Inventory inventory)
        {
            using var scope = _serviceProvider.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<NetworkDbContext>();

            if (context.Inventory == null)
                throw new InvalidOperationException("DbSet<Inventory> is null");

            inventory.LastUpdated = DateTime.Now;
            context.Inventory.Update(inventory);
            await context.SaveChangesAsync().ConfigureAwait(false);
            return inventory;
        }

        public async Task<bool> DeleteAsync(string id)
        {
            using var scope = _serviceProvider.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<NetworkDbContext>();

            if (context.Inventory == null)
                throw new InvalidOperationException("DbSet<Inventory> is null");

            var inventory = await context.Inventory.FindAsync(id).ConfigureAwait(false);
            if (inventory == null) return false;

            context.Inventory.Remove(inventory);
            await context.SaveChangesAsync().ConfigureAwait(false);
            return true;
        }

        public async Task<IEnumerable<Inventory>> SearchAsync(string searchTerm, string? networkFilter = null)
        {
            using var scope = _serviceProvider.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<NetworkDbContext>();

            var query = context.Inventory?.Include(i => i.Network).AsQueryable() ??
                throw new InvalidOperationException("DbSet<Inventory> is null");

            if (!string.IsNullOrEmpty(networkFilter))
            {
                query = query.Where(i => i.NetworkId == networkFilter);
            }

            if (!string.IsNullOrEmpty(searchTerm))
            {
                query = query.Where(i =>
                    i.Name.Contains(searchTerm) ||
                    i.Category.Contains(searchTerm) ||
                    (i.Description ?? "").Contains(searchTerm));
            }

            return await query.OrderBy(i => i.Name).ToListAsync().ConfigureAwait(false);
        }

        public async Task<IEnumerable<Inventory>> GetLowStockItemsAsync(string? networkFilter = null)
        {
            using var scope = _serviceProvider.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<NetworkDbContext>();

            var query = context.Inventory?.Include(i => i.Network).AsQueryable() ??
                throw new InvalidOperationException("DbSet<Inventory> is null");

            if (!string.IsNullOrEmpty(networkFilter))
            {
                query = query.Where(i => i.NetworkId == networkFilter);
            }

            return await query
                .Where(i => i.MinimumStock.HasValue && i.Quantity <= i.MinimumStock.Value)
                .OrderBy(i => i.Quantity)
                .ToListAsync().ConfigureAwait(false);
        }
    }
}

