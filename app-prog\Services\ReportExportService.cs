using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using Microsoft.Win32;
using NetworkManagement.Models;
using NetworkManagement.ViewModels;
using OfficeOpenXml;

namespace NetworkManagement.Services
{
    public class ReportExportService : IReportExportService
    {
        public ReportExportService()
        {
            // Set EPPlus license context
            ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
        }

        #region CSV Export Methods

        public async Task<string> ExportDevicesReportToCsvAsync(IEnumerable<Device> devices, string filePath)
        {
            var csv = new StringBuilder();
            csv.AppendLine("الموقع,النوع,عنوان IP,المسؤول,الشبكة,طريقة الربط,الشبكة المرتبطة,القناة,الأجهزة المتصلة,طول سلك الشبكة,طول سلك الكهرباء,الحالة,تاريخ التثبيت,آخر فحص");

            foreach (var device in devices)
            {
                csv.AppendLine($"\"{device.Location ?? ""}\",\"{device.Type ?? ""}\",\"{device.Ip ?? ""}\",\"{device.Responsible ?? ""}\",\"{device.Network?.Name ?? ""}\",\"{device.ConnectionMethod ?? ""}\",\"{device.LinkedNetwork ?? ""}\",\"{device.Channel?.ToString() ?? ""}\",\"{device.ConnectedDevices?.ToString() ?? ""}\",\"{device.NetworkCableLength?.ToString() ?? ""}\",\"{device.PowerCableLength?.ToString() ?? ""}\",\"{device.Status ?? ""}\",\"{device.InstallDate?.ToString("dd/MM/yyyy") ?? ""}\",\"{device.LastCheck?.ToString("dd/MM/yyyy") ?? ""}\"");
            }

            await File.WriteAllTextAsync(filePath, csv.ToString(), Encoding.UTF8);
            return filePath;
        }

        public async Task<string> ExportSitesReportToCsvAsync(IEnumerable<Site> sites, string filePath)
        {
            var csv = new StringBuilder();
            csv.AppendLine("الاسم,العنوان,خط الطول,خط العرض,مصدر الطاقة,سعة التخزين,الاستهلاك اليومي,قاعدة التثبيت,الصناديق,طول السلك,الشبكة");

            foreach (var site in sites)
            {
                csv.AppendLine($"\"{site.Name ?? ""}\",\"{site.Address ?? ""}\",\"{site.GpsLng?.ToString() ?? ""}\",\"{site.GpsLat?.ToString() ?? ""}\",\"{site.PowerSource ?? ""}\",\"{site.StorageCapacity?.ToString() ?? ""}\",\"{site.DailyConsumption?.ToString() ?? ""}\",\"{site.InstallationBase ?? ""}\",\"{site.Boxes?.ToString() ?? ""}\",\"{site.WireLength?.ToString() ?? ""}\",\"{site.Network?.Name ?? ""}\"");
            }

            await File.WriteAllTextAsync(filePath, csv.ToString(), Encoding.UTF8);
            return filePath;
        }

        public async Task<string> ExportUsersReportToCsvAsync(IEnumerable<User> users, string filePath)
        {
            var csv = new StringBuilder();
            csv.AppendLine("اسم المستخدم,الاسم,الدور,الشبكة,تاريخ الإنشاء");

            foreach (var user in users)
            {
                csv.AppendLine($"\"{user.Username ?? ""}\",\"{user.Name ?? ""}\",\"{user.Role ?? ""}\",\"{user.Network?.Name ?? ""}\",\"{user.CreatedAt.ToString("dd/MM/yyyy") ?? ""}\"");
            }

            await File.WriteAllTextAsync(filePath, csv.ToString(), Encoding.UTF8);
            return filePath;
        }

        public async Task<string> ExportPurchasesReportToCsvAsync(IEnumerable<Purchase> purchases, string filePath)
        {
            var csv = new StringBuilder();
            csv.AppendLine("نوع الصنف,الكمية,الوحدة,السعر,التاريخ,المورد,الشبكة,الوصف,رقم الفاتورة,الفئة");

            foreach (var purchase in purchases)
            {
                csv.AppendLine($"\"{purchase.ItemType ?? ""}\",\"{purchase.Quantity}\",\"{purchase.Unit ?? ""}\",\"{purchase.Price:C}\",\"{purchase.Date.ToString("dd/MM/yyyy")}\",\"{purchase.Supplier ?? ""}\",\"{purchase.Network?.Name ?? ""}\",\"{purchase.Description ?? ""}\",\"{purchase.InvoiceNumber ?? ""}\",\"{purchase.Category ?? ""}\"");
            }

            await File.WriteAllTextAsync(filePath, csv.ToString(), Encoding.UTF8);
            return filePath;
        }

        public async Task<string> ExportInventoryReportToCsvAsync(IEnumerable<Inventory> inventory, string filePath)
        {
            var csv = new StringBuilder();
            csv.AppendLine("الاسم,الفئة,الكمية,الوحدة,الحد الأدنى,الحد الأقصى,سعر الوحدة,الموقع,المورد,الشبكة,الوصف");

            foreach (var item in inventory)
            {
                csv.AppendLine($"\"{item.Name ?? ""}\",\"{item.Category ?? ""}\",\"{item.Quantity}\",\"{item.Unit ?? ""}\",\"{item.MinimumStock}\",\"{item.MaximumStock}\",\"{item.UnitPrice:C}\",\"{item.Location ?? ""}\",\"{item.Supplier ?? ""}\",\"{item.Network?.Name ?? ""}\",\"{item.Description ?? ""}\"");
            }

            await File.WriteAllTextAsync(filePath, csv.ToString(), Encoding.UTF8);
            return filePath;
        }

        public async Task<string> ExportTasksReportToCsvAsync(IEnumerable<Models.Task> tasks, string filePath)
        {
            var csv = new StringBuilder();
            csv.AppendLine("الوصف,الحالة,الأولوية,تاريخ الإنشاء,تاريخ الاستحقاق,تاريخ الإكمال,المستخدم,الشبكة,الملاحظات");

            foreach (var task in tasks)
            {
                csv.AppendLine($"\"{task.Description ?? ""}\",\"{task.StatusDisplay}\",\"{task.PriorityDisplay}\",\"{task.CreatedAt.ToString("dd/MM/yyyy")}\",\"{task.DueDate?.ToString("dd/MM/yyyy") ?? ""}\",\"{task.CompletedAt?.ToString("dd/MM/yyyy") ?? ""}\",\"{task.User?.Name ?? ""}\",\"{task.Network?.Name ?? ""}\",\"{task.Notes ?? ""}\"");
            }

            await File.WriteAllTextAsync(filePath, csv.ToString(), Encoding.UTF8);
            return filePath;
        }

        public async Task<string> ExportNetworkStatisticsToCsvAsync(IEnumerable<NetworkStatistic> statistics, string filePath)
        {
            var csv = new StringBuilder();
            csv.AppendLine("الشبكة,عدد الأجهزة,نشط,غير نشط");

            foreach (var stat in statistics)
            {
                csv.AppendLine($"\"{stat.NetworkName}\",\"{stat.DeviceCount}\",\"{stat.ActiveCount}\",\"{stat.InactiveCount}\"");
            }

            await File.WriteAllTextAsync(filePath, csv.ToString(), Encoding.UTF8);
            return filePath;
        }

        public async Task<string> ExportMonthlyPurchasesToCsvAsync(IEnumerable<MonthlyPurchase> purchases, string filePath)
        {
            var csv = new StringBuilder();
            csv.AppendLine("الشهر,المبلغ,عدد المشتريات");

            foreach (var purchase in purchases)
            {
                csv.AppendLine($"\"{purchase.Month}\",\"{purchase.Amount:C}\",\"{purchase.Count}\"");
            }

            await File.WriteAllTextAsync(filePath, csv.ToString(), Encoding.UTF8);
            return filePath;
        }

        public async Task<string> ExportDeviceStatusCountsToCsvAsync(IEnumerable<DeviceStatusCount> statusCounts, string filePath)
        {
            var csv = new StringBuilder();
            csv.AppendLine("الحالة,العدد,النسبة %");

            foreach (var status in statusCounts)
            {
                csv.AppendLine($"\"{status.Status}\",\"{status.Count}\",\"{status.Percentage:F1}\"");
            }

            await File.WriteAllTextAsync(filePath, csv.ToString(), Encoding.UTF8);
            return filePath;
        }

        public async Task<string> ExportGeneralReportToCsvAsync(ReportsViewModel viewModel, string filePath)
        {
            var csv = new StringBuilder();

            // General Statistics
            csv.AppendLine("=== الإحصائيات العامة ===");
            csv.AppendLine($"إجمالي الأجهزة,{viewModel.TotalDevices}");
            csv.AppendLine($"الأجهزة النشطة,{viewModel.ActiveDevices}");
            csv.AppendLine($"إجمالي المواقع,{viewModel.TotalSites}");
            csv.AppendLine($"إجمالي المستخدمين,{viewModel.TotalUsers}");
            csv.AppendLine($"إجمالي المشتريات,{viewModel.TotalPurchases:C}");
            csv.AppendLine($"إجمالي المخزون,{viewModel.TotalInventoryItems}");
            csv.AppendLine($"المخزون المنخفض,{viewModel.LowStockItems}");
            csv.AppendLine();

            await File.WriteAllTextAsync(filePath, csv.ToString(), Encoding.UTF8);
            return filePath;
        }

        #endregion

        #region Excel Export Methods

        public async Task<string> ExportDevicesReportToExcelAsync(IEnumerable<Device> devices, string filePath)
        {
            using var package = new ExcelPackage();
            var worksheet = package.Workbook.Worksheets.Add("الأجهزة");

            // Headers
            var headers = new[] { "الموقع", "النوع", "عنوان IP", "المسؤول", "الشبكة", "طريقة الربط", "الشبكة المرتبطة", "القناة", "الأجهزة المتصلة", "طول سلك الشبكة", "طول سلك الكهرباء", "الحالة", "تاريخ التثبيت", "آخر فحص" };
            for (int i = 0; i < headers.Length; i++)
            {
                worksheet.Cells[1, i + 1].Value = headers[i];
                worksheet.Cells[1, i + 1].Style.Font.Bold = true;
            }

            // Data
            int row = 2;
            foreach (var device in devices)
            {
                worksheet.Cells[row, 1].Value = device.Location ?? "";
                worksheet.Cells[row, 2].Value = device.Type ?? "";
                worksheet.Cells[row, 3].Value = device.Ip ?? "";
                worksheet.Cells[row, 4].Value = device.Responsible ?? "";
                worksheet.Cells[row, 5].Value = device.Network?.Name ?? "";
                worksheet.Cells[row, 6].Value = device.ConnectionMethod ?? "";
                worksheet.Cells[row, 7].Value = device.LinkedNetwork ?? "";
                worksheet.Cells[row, 8].Value = device.Channel?.ToString() ?? "";
                worksheet.Cells[row, 9].Value = device.ConnectedDevices?.ToString() ?? "";
                worksheet.Cells[row, 10].Value = device.NetworkCableLength?.ToString() ?? "";
                worksheet.Cells[row, 11].Value = device.PowerCableLength?.ToString() ?? "";
                worksheet.Cells[row, 12].Value = device.Status ?? "";
                worksheet.Cells[row, 13].Value = device.InstallDate?.ToString("dd/MM/yyyy") ?? "";
                worksheet.Cells[row, 14].Value = device.LastCheck?.ToString("dd/MM/yyyy") ?? "";
                row++;
            }

            // Auto-fit columns
            worksheet.Cells.AutoFitColumns();

            await package.SaveAsAsync(new FileInfo(filePath));
            return filePath;
        }

        public async Task<string> ExportSitesReportToExcelAsync(IEnumerable<Site> sites, string filePath)
        {
            using var package = new ExcelPackage();
            var worksheet = package.Workbook.Worksheets.Add("المواقع");

            // Headers
            var headers = new[] { "الاسم", "العنوان", "خط الطول", "خط العرض", "مصدر الطاقة", "سعة التخزين", "الاستهلاك اليومي", "قاعدة التثبيت", "الصناديق", "طول السلك", "الشبكة" };
            for (int i = 0; i < headers.Length; i++)
            {
                worksheet.Cells[1, i + 1].Value = headers[i];
                worksheet.Cells[1, i + 1].Style.Font.Bold = true;
            }

            // Data
            int row = 2;
            foreach (var site in sites)
            {
                worksheet.Cells[row, 1].Value = site.Name ?? "";
                worksheet.Cells[row, 2].Value = site.Address ?? "";
                worksheet.Cells[row, 3].Value = site.GpsLng?.ToString() ?? "";
                worksheet.Cells[row, 4].Value = site.GpsLat?.ToString() ?? "";
                worksheet.Cells[row, 5].Value = site.PowerSource ?? "";
                worksheet.Cells[row, 6].Value = site.StorageCapacity?.ToString() ?? "";
                worksheet.Cells[row, 7].Value = site.DailyConsumption?.ToString() ?? "";
                worksheet.Cells[row, 8].Value = site.InstallationBase ?? "";
                worksheet.Cells[row, 9].Value = site.Boxes?.ToString() ?? "";
                worksheet.Cells[row, 10].Value = site.WireLength?.ToString() ?? "";
                worksheet.Cells[row, 11].Value = site.Network?.Name ?? "";
                row++;
            }

            worksheet.Cells.AutoFitColumns();
            await package.SaveAsAsync(new FileInfo(filePath));
            return filePath;
        }

        public async Task<string> ExportUsersReportToExcelAsync(IEnumerable<User> users, string filePath)
        {
            using var package = new ExcelPackage();
            var worksheet = package.Workbook.Worksheets.Add("المستخدمين");

            // Headers
            var headers = new[] { "اسم المستخدم", "الاسم", "الدور", "الشبكة", "تاريخ الإنشاء" };
            for (int i = 0; i < headers.Length; i++)
            {
                worksheet.Cells[1, i + 1].Value = headers[i];
                worksheet.Cells[1, i + 1].Style.Font.Bold = true;
            }

            // Data
            int row = 2;
            foreach (var user in users)
            {
                worksheet.Cells[row, 1].Value = user.Username ?? "";
                worksheet.Cells[row, 2].Value = user.Name ?? "";
                worksheet.Cells[row, 3].Value = user.Role ?? "";
                worksheet.Cells[row, 4].Value = user.Network?.Name ?? "";
                worksheet.Cells[row, 5].Value = user.CreatedAt.ToString("dd/MM/yyyy");
                row++;
            }

            worksheet.Cells.AutoFitColumns();
            await package.SaveAsAsync(new FileInfo(filePath));
            return filePath;
        }

        public async Task<string> ExportPurchasesReportToExcelAsync(IEnumerable<Purchase> purchases, string filePath)
        {
            using var package = new ExcelPackage();
            var worksheet = package.Workbook.Worksheets.Add("المشتريات");

            // Headers
            var headers = new[] { "نوع الصنف", "الكمية", "الوحدة", "السعر", "التاريخ", "المورد", "الشبكة", "الوصف", "رقم الفاتورة", "الفئة" };
            for (int i = 0; i < headers.Length; i++)
            {
                worksheet.Cells[1, i + 1].Value = headers[i];
                worksheet.Cells[1, i + 1].Style.Font.Bold = true;
            }

            // Data
            int row = 2;
            foreach (var purchase in purchases)
            {
                worksheet.Cells[row, 1].Value = purchase.ItemType ?? "";
                worksheet.Cells[row, 2].Value = purchase.Quantity;
                worksheet.Cells[row, 3].Value = purchase.Unit ?? "";
                worksheet.Cells[row, 4].Value = purchase.Price;
                worksheet.Cells[row, 5].Value = purchase.Date.ToString("dd/MM/yyyy");
                worksheet.Cells[row, 6].Value = purchase.Supplier ?? "";
                worksheet.Cells[row, 7].Value = purchase.Network?.Name ?? "";
                worksheet.Cells[row, 8].Value = purchase.Description ?? "";
                worksheet.Cells[row, 9].Value = purchase.InvoiceNumber ?? "";
                worksheet.Cells[row, 10].Value = purchase.Category ?? "";
                row++;
            }

            worksheet.Cells.AutoFitColumns();
            await package.SaveAsAsync(new FileInfo(filePath));
            return filePath;
        }

        public async Task<string> ExportInventoryReportToExcelAsync(IEnumerable<Inventory> inventory, string filePath)
        {
            using var package = new ExcelPackage();
            var worksheet = package.Workbook.Worksheets.Add("المخزون");

            // Headers
            var headers = new[] { "الاسم", "الفئة", "الكمية", "الوحدة", "الحد الأدنى", "الحد الأقصى", "سعر الوحدة", "الموقع", "المورد", "الشبكة", "الوصف" };
            for (int i = 0; i < headers.Length; i++)
            {
                worksheet.Cells[1, i + 1].Value = headers[i];
                worksheet.Cells[1, i + 1].Style.Font.Bold = true;
            }

            // Data
            int row = 2;
            foreach (var item in inventory)
            {
                worksheet.Cells[row, 1].Value = item.Name ?? "";
                worksheet.Cells[row, 2].Value = item.Category ?? "";
                worksheet.Cells[row, 3].Value = item.Quantity;
                worksheet.Cells[row, 4].Value = item.Unit ?? "";
                worksheet.Cells[row, 5].Value = item.MinimumStock;
                worksheet.Cells[row, 6].Value = item.MaximumStock;
                worksheet.Cells[row, 7].Value = item.UnitPrice;
                worksheet.Cells[row, 8].Value = item.Location ?? "";
                worksheet.Cells[row, 9].Value = item.Supplier ?? "";
                worksheet.Cells[row, 10].Value = item.Network?.Name ?? "";
                worksheet.Cells[row, 11].Value = item.Description ?? "";
                row++;
            }

            worksheet.Cells.AutoFitColumns();
            await package.SaveAsAsync(new FileInfo(filePath));
            return filePath;
        }

        public async Task<string> ExportTasksReportToExcelAsync(IEnumerable<Models.Task> tasks, string filePath)
        {
            using var package = new ExcelPackage();
            var worksheet = package.Workbook.Worksheets.Add("المهام");

            // Headers
            var headers = new[] { "الوصف", "الحالة", "الأولوية", "تاريخ الإنشاء", "تاريخ الاستحقاق", "تاريخ الإكمال", "المستخدم", "الشبكة", "الملاحظات" };
            for (int i = 0; i < headers.Length; i++)
            {
                worksheet.Cells[1, i + 1].Value = headers[i];
                worksheet.Cells[1, i + 1].Style.Font.Bold = true;
            }

            // Data
            int row = 2;
            foreach (var task in tasks)
            {
                worksheet.Cells[row, 1].Value = task.Description ?? "";
                worksheet.Cells[row, 2].Value = task.StatusDisplay;
                worksheet.Cells[row, 3].Value = task.PriorityDisplay;
                worksheet.Cells[row, 4].Value = task.CreatedAt.ToString("dd/MM/yyyy");
                worksheet.Cells[row, 5].Value = task.DueDate?.ToString("dd/MM/yyyy") ?? "";
                worksheet.Cells[row, 6].Value = task.CompletedAt?.ToString("dd/MM/yyyy") ?? "";
                worksheet.Cells[row, 7].Value = task.User?.Name ?? "";
                worksheet.Cells[row, 8].Value = task.Network?.Name ?? "";
                worksheet.Cells[row, 9].Value = task.Notes ?? "";
                row++;
            }

            worksheet.Cells.AutoFitColumns();
            await package.SaveAsAsync(new FileInfo(filePath));
            return filePath;
        }

        public async Task<string> ExportCompleteReportToExcelAsync(ReportsViewModel viewModel, string filePath)
        {
            using var package = new ExcelPackage();

            // Summary Sheet
            var summarySheet = package.Workbook.Worksheets.Add("الملخص");
            summarySheet.Cells[1, 1].Value = "الإحصائيات العامة";
            summarySheet.Cells[1, 1].Style.Font.Bold = true;
            summarySheet.Cells[1, 1].Style.Font.Size = 16;

            summarySheet.Cells[3, 1].Value = "إجمالي الأجهزة";
            summarySheet.Cells[3, 2].Value = viewModel.TotalDevices;
            summarySheet.Cells[4, 1].Value = "الأجهزة النشطة";
            summarySheet.Cells[4, 2].Value = viewModel.ActiveDevices;
            summarySheet.Cells[5, 1].Value = "إجمالي المواقع";
            summarySheet.Cells[5, 2].Value = viewModel.TotalSites;
            summarySheet.Cells[6, 1].Value = "إجمالي المستخدمين";
            summarySheet.Cells[6, 2].Value = viewModel.TotalUsers;
            summarySheet.Cells[7, 1].Value = "إجمالي المشتريات";
            summarySheet.Cells[7, 2].Value = viewModel.TotalPurchases;
            summarySheet.Cells[8, 1].Value = "إجمالي المخزون";
            summarySheet.Cells[8, 2].Value = viewModel.TotalInventoryItems;
            summarySheet.Cells[9, 1].Value = "المخزون المنخفض";
            summarySheet.Cells[9, 2].Value = viewModel.LowStockItems;

            summarySheet.Cells.AutoFitColumns();

            await package.SaveAsAsync(new FileInfo(filePath));
            return filePath;
        }

        #endregion

        #region Import Methods - Will be implemented in next part

        public async System.Threading.Tasks.Task<IEnumerable<Device>> ImportDevicesFromCsvAsync(string filePath)
        {
            var devices = new List<Device>();

            try
            {
                var lines = await File.ReadAllLinesAsync(filePath, Encoding.UTF8);
                if (lines.Length <= 1) return devices; // No data or header only

                // Skip header line
                for (int i = 1; i < lines.Length; i++)
                {
                    var line = lines[i];
                    if (string.IsNullOrWhiteSpace(line)) continue;

                    var values = ParseCsvLine(line);
                    if (values.Length >= 8) // Minimum required fields
                    {
                        var device = new Device
                        {
                            Id = Guid.NewGuid().ToString(),
                            Location = values[0],
                            Type = values[1],
                            Ip = values[2],
                            Responsible = values[3],
                            NetworkId = values[4],
                            ConnectionMethod = values[5],
                            LinkedNetwork = values[6],
                            Channel = int.TryParse(values[7], out var channel) ? channel : null,
                            ConnectedDevices = values.Length > 8 && int.TryParse(values[8], out var connected) ? connected : null,
                            NetworkCableLength = values.Length > 9 && int.TryParse(values[9], out var netCable) ? netCable : null,
                            PowerCableLength = values.Length > 10 && int.TryParse(values[10], out var powerCable) ? powerCable : null,
                            Status = values.Length > 11 ? values[11] : "غير محدد",
                            InstallDate = values.Length > 12 && DateTime.TryParse(values[12], out var installDate) ? installDate : DateTime.Now,
                            LastCheck = values.Length > 13 && DateTime.TryParse(values[13], out var lastCheck) ? lastCheck : null
                        };
                        devices.Add(device);
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في قراءة ملف CSV: {ex.Message}");
            }

            return devices;
        }

        public async System.Threading.Tasks.Task<IEnumerable<Device>> ImportDevicesFromExcelAsync(string filePath)
        {
            var devices = new List<Device>();

            try
            {
                return await System.Threading.Tasks.Task.Run(() =>
                {
                    using var package = new ExcelPackage(new FileInfo(filePath));
                    var worksheet = package.Workbook.Worksheets.FirstOrDefault();
                    if (worksheet == null) return devices;

                    var rowCount = worksheet.Dimension?.Rows ?? 0;
                    if (rowCount <= 1) return devices; // No data or header only

                    // Start from row 2 (skip header)
                    for (int row = 2; row <= rowCount; row++)
                    {
                        var location = worksheet.Cells[row, 1].Text;
                        var type = worksheet.Cells[row, 2].Text;
                        var ip = worksheet.Cells[row, 3].Text;
                        var responsible = worksheet.Cells[row, 4].Text;

                        // Skip empty rows
                        if (string.IsNullOrWhiteSpace(location) && string.IsNullOrWhiteSpace(responsible)) continue;

                        var device = new Device
                        {
                            Id = Guid.NewGuid().ToString(),
                            Location = location,
                            Type = type,
                            Ip = ip,
                            Responsible = responsible,
                            NetworkId = worksheet.Cells[row, 5].Text,
                            ConnectionMethod = worksheet.Cells[row, 6].Text,
                            LinkedNetwork = worksheet.Cells[row, 7].Text,
                            Channel = int.TryParse(worksheet.Cells[row, 8].Text, out var channel) ? channel : null,
                            ConnectedDevices = int.TryParse(worksheet.Cells[row, 9].Text, out var connected) ? connected : null,
                            NetworkCableLength = int.TryParse(worksheet.Cells[row, 10].Text, out var netCable) ? netCable : null,
                            PowerCableLength = int.TryParse(worksheet.Cells[row, 11].Text, out var powerCable) ? powerCable : null,
                            Status = !string.IsNullOrWhiteSpace(worksheet.Cells[row, 12].Text) ? worksheet.Cells[row, 12].Text : "غير محدد",
                            InstallDate = DateTime.TryParse(worksheet.Cells[row, 13].Text, out var installDate) ? installDate : DateTime.Now,
                            LastCheck = DateTime.TryParse(worksheet.Cells[row, 14].Text, out var lastCheck) ? lastCheck : null
                        };
                        devices.Add(device);
                    }

                    return devices;
                });
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في قراءة ملف Excel: {ex.Message}");
            }
        }

        public async System.Threading.Tasks.Task<IEnumerable<Site>> ImportSitesFromCsvAsync(string filePath)
        {
            // TODO: Implement CSV import for sites
            await System.Threading.Tasks.Task.Delay(1);
            return new List<Site>();
        }

        public async System.Threading.Tasks.Task<IEnumerable<Site>> ImportSitesFromExcelAsync(string filePath)
        {
            // TODO: Implement Excel import for sites
            await System.Threading.Tasks.Task.Delay(1);
            return new List<Site>();
        }

        public async System.Threading.Tasks.Task<IEnumerable<User>> ImportUsersFromCsvAsync(string filePath)
        {
            // TODO: Implement CSV import for users
            await System.Threading.Tasks.Task.Delay(1);
            return new List<User>();
        }

        public async System.Threading.Tasks.Task<IEnumerable<User>> ImportUsersFromExcelAsync(string filePath)
        {
            // TODO: Implement Excel import for users
            await System.Threading.Tasks.Task.Delay(1);
            return new List<User>();
        }

        public async System.Threading.Tasks.Task<IEnumerable<Purchase>> ImportPurchasesFromCsvAsync(string filePath)
        {
            // TODO: Implement CSV import for purchases
            await System.Threading.Tasks.Task.Delay(1);
            return new List<Purchase>();
        }

        public async System.Threading.Tasks.Task<IEnumerable<Purchase>> ImportPurchasesFromExcelAsync(string filePath)
        {
            // TODO: Implement Excel import for purchases
            await System.Threading.Tasks.Task.Delay(1);
            return new List<Purchase>();
        }

        public async System.Threading.Tasks.Task<IEnumerable<Inventory>> ImportInventoryFromCsvAsync(string filePath)
        {
            // TODO: Implement CSV import for inventory
            await System.Threading.Tasks.Task.Delay(1);
            return new List<Inventory>();
        }

        public async System.Threading.Tasks.Task<IEnumerable<Inventory>> ImportInventoryFromExcelAsync(string filePath)
        {
            // TODO: Implement Excel import for inventory
            await System.Threading.Tasks.Task.Delay(1);
            return new List<Inventory>();
        }

        #endregion

        #region Utility Methods

        public async System.Threading.Tasks.Task<string> GetSaveFilePathAsync(string defaultFileName, string filter)
        {
            await System.Threading.Tasks.Task.Delay(1); // Make it async

            var saveFileDialog = new SaveFileDialog
            {
                FileName = defaultFileName,
                Filter = filter,
                DefaultExt = filter.Contains("xlsx") ? "xlsx" : "csv"
            };

            return saveFileDialog.ShowDialog() == true ? saveFileDialog.FileName : string.Empty;
        }

        public async System.Threading.Tasks.Task<string> GetOpenFilePathAsync(string filter)
        {
            await System.Threading.Tasks.Task.Delay(1); // Make it async

            var openFileDialog = new OpenFileDialog
            {
                Filter = filter,
                Multiselect = false
            };

            return openFileDialog.ShowDialog() == true ? openFileDialog.FileName : string.Empty;
        }

        #endregion

        #region Helper Methods

        private string[] ParseCsvLine(string line)
        {
            var result = new List<string>();
            var current = new StringBuilder();
            bool inQuotes = false;

            for (int i = 0; i < line.Length; i++)
            {
                char c = line[i];

                if (c == '"')
                {
                    if (inQuotes && i + 1 < line.Length && line[i + 1] == '"')
                    {
                        // Escaped quote
                        current.Append('"');
                        i++; // Skip next quote
                    }
                    else
                    {
                        inQuotes = !inQuotes;
                    }
                }
                else if (c == ',' && !inQuotes)
                {
                    result.Add(current.ToString());
                    current.Clear();
                }
                else
                {
                    current.Append(c);
                }
            }

            result.Add(current.ToString());
            return result.ToArray();
        }

        #endregion
    }
}
